{"version": 2, "dgSpecHash": "HNiYBnKTJPY=", "success": false, "projectFilePath": "Z:\\Projects\\Tools\\OpenUSD.Net\\samples\\ConsoleSample\\ConsoleSample.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1104", "level": "Error", "message": "Unable to find project 'Z:\\Projects\\Tools\\OpenUSD.Net\\src\\OpenUSD.Net\\OpenUSD.Net.csproj'. Check that the project reference is valid and that the project file exists.", "projectPath": "Z:\\Projects\\Tools\\OpenUSD.Net\\samples\\ConsoleSample\\ConsoleSample.csproj", "filePath": "Z:\\Projects\\Tools\\OpenUSD.Net\\samples\\ConsoleSample\\ConsoleSample.csproj", "libraryId": "Z:\\Projects\\Tools\\OpenUSD.Net\\src\\OpenUSD.Net\\OpenUSD.Net.csproj", "targetGraphs": ["net8.0"]}]}