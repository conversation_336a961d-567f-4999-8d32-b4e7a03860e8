using System;
using OpenUSD.Net;

namespace ConsoleSample
{
    internal class Program
    {
        static void Main(string[] args)
        {
            try
            {
                // Path to save the USD file (relative path)
                string path = "test.usda";
                
                Console.WriteLine($"Creating new USD stage at: {path}");
                
                // Create a new USD stage
                using var stage = Stage.CreateNew(path);
                
                // Define a prim on the stage
                var prim = stage.DefinePrim("/World/MyXform", "Xform");
                Console.WriteLine($"Created prim: {prim.Name}");
                
                // Create and set a string attribute
                prim.CreateStringAttribute("comment");
                prim.SetStringAttribute("comment", "Hello from C#");
                Console.WriteLine("Set 'comment' attribute");
                
                // Save the stage
                stage.Save();
                Console.WriteLine($"Saved: {path}");
            }
            catch (OpenUSDException ex)
            {
                Console.WriteLine($"OpenUSD error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }
    }
}
