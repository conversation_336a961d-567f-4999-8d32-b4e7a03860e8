/**
 * @file ousd.h
 * @brief C API for OpenUSD library
 * 
 * This header defines a C-compatible API for working with OpenUSD.
 * All strings are expected to be UTF-8 encoded.
 */

#ifndef OUSD_H
#define OUSD_H

#ifdef __cplusplus
extern "C" {
#endif

/* Define export/import macros for Windows DLL */
#if defined(_WIN32) || defined(_WIN64)
    #ifdef OUSD_EXPORTS
        #define OUSD_API __declspec(dllexport)
    #else
        #define OUSD_API __declspec(dllimport)
    #endif
#else
    #define OUSD_API
#endif

/* Opaque handle structs */
typedef struct OUSDStage OUSDStage;
typedef struct OUSDPrim OUSDPrim;

/**
 * @brief Creates a new USD stage at the specified path
 * 
 * @param path UTF-8 file path where the stage will be saved
 * @param outStage Pointer to receive the stage handle
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_stage_create_new(const char* path, void** outStage);

/**
 * @brief Opens an existing USD stage from the specified path
 * 
 * @param path UTF-8 file path to the USD file
 * @param outStage Pointer to receive the stage handle
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_stage_open(const char* path, void** outStage);

/**
 * @brief Saves the stage to disk
 * 
 * @param stage Handle to the stage
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_stage_save(void* stage);

/**
 * @brief Releases the stage and frees associated resources
 * 
 * @param stage Handle to the stage
 */
OUSD_API void ousd_stage_release(void* stage);

/**
 * @brief Defines a new prim on the stage
 * 
 * @param stage Handle to the stage
 * @param primPath UTF-8 path for the prim (e.g., "/Root/MyPrim")
 * @param typeName UTF-8 type name (e.g., "Xform", "Mesh", etc.)
 * @param outPrim Pointer to receive the prim handle
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_define_prim(void* stage, const char* primPath, const char* typeName, void** outPrim);

/**
 * @brief Gets an existing prim from the stage
 * 
 * @param stage Handle to the stage
 * @param primPath UTF-8 path for the prim
 * @param outPrim Pointer to receive the prim handle
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_get_prim(void* stage, const char* primPath, void** outPrim);

/**
 * @brief Checks if a prim handle is valid
 * 
 * @param prim Handle to the prim
 * @param outValid Pointer to receive validity status (0 for invalid, 1 for valid)
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_prim_is_valid(void* prim, int* outValid);

/**
 * @brief Gets the name of a prim
 * 
 * @param prim Handle to the prim
 * @param buffer Buffer to receive the UTF-8 name
 * @param bufferLen Length of the buffer in bytes
 * @param outWritten Pointer to receive the number of bytes written (including null terminator)
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_prim_get_name(void* prim, char* buffer, int bufferLen, int* outWritten);

/**
 * @brief Releases the prim and frees associated resources
 * 
 * @param prim Handle to the prim
 */
OUSD_API void ousd_prim_release(void* prim);

/**
 * @brief Creates a string attribute on a prim
 * 
 * @param prim Handle to the prim
 * @param attrName UTF-8 name of the attribute
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_prim_create_string_attribute(void* prim, const char* attrName);

/**
 * @brief Sets a string attribute value on a prim
 * 
 * @param prim Handle to the prim
 * @param attrName UTF-8 name of the attribute
 * @param value UTF-8 value to set
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_prim_set_string_attribute(void* prim, const char* attrName, const char* value);

/**
 * @brief Gets a string attribute value from a prim
 * 
 * @param prim Handle to the prim
 * @param attrName UTF-8 name of the attribute
 * @param buffer Buffer to receive the UTF-8 value
 * @param bufferLen Length of the buffer in bytes
 * @param outWritten Pointer to receive the number of bytes written (including null terminator)
 * @param outFound Pointer to receive whether the attribute was found (0 for not found, 1 for found)
 * @return int 0 on success, non-zero on failure
 */
OUSD_API int ousd_prim_get_string_attribute(void* prim, const char* attrName, char* buffer, int bufferLen, int* outWritten, int* outFound);

/**
 * @brief Gets the last error message
 * 
 * @return const char* UTF-8 error message, valid until the next API call in the same thread
 */
OUSD_API const char* ousd_last_error_message();

#ifdef __cplusplus
}
#endif

#endif /* OUSD_H */
