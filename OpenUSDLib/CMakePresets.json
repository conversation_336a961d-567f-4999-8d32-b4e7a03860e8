{"version": 3, "cmakeMinimumRequired": {"major": 3, "minor": 20, "patch": 0}, "configurePresets": [{"name": "windows-x64", "displayName": "Windows x64", "description": "Target Windows with Visual Studio 2022 (x64 architecture)", "generator": "Visual Studio 17 2022", "architecture": "x64", "binaryDir": "${sourceDir}/out/build/${presetName}", "cacheVariables": {"CMAKE_INSTALL_PREFIX": "${sourceDir}/out/install/${presetName}", "USD_ROOT": "$env{USD_INSTALL_ROOT}"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "windows-arm64", "displayName": "Windows ARM64", "description": "Target Windows with Visual Studio 2022 (ARM64 architecture)", "generator": "Visual Studio 17 2022", "architecture": "ARM64", "binaryDir": "${sourceDir}/out/build/${presetName}", "cacheVariables": {"CMAKE_INSTALL_PREFIX": "${sourceDir}/out/install/${presetName}", "USD_ROOT": "$env{USD_INSTALL_ROOT}"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}], "buildPresets": [{"name": "windows-x64", "displayName": "Windows x64", "configurePreset": "windows-x64", "configuration": "Release"}, {"name": "windows-arm64", "displayName": "Windows ARM64", "configurePreset": "windows-arm64", "configuration": "Release"}], "installPresets": [{"name": "windows-x64", "displayName": "Windows x64", "configurePreset": "windows-x64", "installDir": "${sourceDir}/out/install/${presetName}"}, {"name": "windows-arm64", "displayName": "Windows ARM64", "configurePreset": "windows-arm64", "installDir": "${sourceDir}/out/install/${presetName}"}]}