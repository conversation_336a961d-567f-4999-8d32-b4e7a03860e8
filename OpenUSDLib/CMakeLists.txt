cmake_minimum_required(VERSION 3.20)
project(ousd_native LANGUAGES CXX)

# Set C++17 standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Find OpenUSD package
find_package(pxr CONFIG REQUIRED)

# Add shared library
add_library(ousd_native SHARED 
    src/ousd.cpp
    include/ousd.h
)

# Include directories
target_include_directories(ousd_native 
    PUBLIC 
        ${CMAKE_CURRENT_SOURCE_DIR}/include
    PRIVATE
        ${PXR_INCLUDE_DIRS}
)

# Define export macro
target_compile_definitions(ousd_native 
    PRIVATE 
        OUSD_EXPORTS
)

# Link against OpenUSD libraries
target_link_libraries(ousd_native 
    PRIVATE 
        pxr::usd
        pxr::tf
        pxr::sdf
        pxr::vt
        pxr::gf
)

# MSVC-specific settings
if(MSVC)
    add_compile_definitions(UNICODE _UNICODE)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
endif()

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Install targets
install(TARGETS ousd_native
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION bin
    ARCHIVE DESTINATION lib
)
