#define OUSD_EXPORTS
#include "../include/ousd.h"

#include <pxr/usd/usd/stage.h>
#include <pxr/usd/usd/prim.h>
#include <pxr/usd/sdf/path.h>
#include <pxr/usd/usd/attribute.h>
#include <pxr/base/tf/token.h>
#include <pxr/usd/sdf/valueTypeName.h>
#include <pxr/base/vt/value.h>

#include <string>
#include <cstring>
#include <memory>
#include <thread>
#include <mutex>
#include <unordered_map>

// Thread-local error handling
static thread_local std::string g_lastError;

// Set the last error message
static void SetLastError(const std::string& error) {
    g_lastError = error;
}

// Get the last error message
const char* ousd_last_error_message() {
    return g_lastError.c_str();
}

// Define opaque struct implementations
struct OUSDStage {
    pxr::UsdStageRefPtr stage;
    
    explicit OUSDStage(pxr::UsdStageRefPtr inStage) : stage(inStage) {}
};

struct OUSDPrim {
    pxr::UsdPrim prim;
    
    explicit OUSDPrim(const pxr::UsdPrim& inPrim) : prim(inPrim) {}
};

// Helper function to copy strings safely
static int CopyStringToBuffer(const std::string& str, char* buffer, int bufferLen, int* outWritten) {
    if (!buffer || bufferLen <= 0 || !outWritten) {
        SetLastError("Invalid buffer parameters");
        return 1;
    }
    
    size_t strLen = str.length();
    size_t bytesToCopy = std::min(static_cast<size_t>(bufferLen - 1), strLen);
    
    if (bytesToCopy > 0) {
        std::memcpy(buffer, str.c_str(), bytesToCopy);
    }
    
    buffer[bytesToCopy] = '\0';
    *outWritten = static_cast<int>(bytesToCopy + 1); // Include null terminator
    
    return 0;
}

// Implementation of API functions
int ousd_stage_create_new(const char* path, void** outStage) {
    if (!path || !outStage) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        pxr::UsdStageRefPtr stage = pxr::UsdStage::CreateNew(path);
        if (!stage) {
            SetLastError("Failed to create new stage");
            return 1;
        }
        
        *outStage = new OUSDStage(stage);
        return 0;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception creating stage: ") + e.what());
        return 1;
    }
}

int ousd_stage_open(const char* path, void** outStage) {
    if (!path || !outStage) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        pxr::UsdStageRefPtr stage = pxr::UsdStage::Open(path);
        if (!stage) {
            SetLastError("Failed to open stage");
            return 1;
        }
        
        *outStage = new OUSDStage(stage);
        return 0;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception opening stage: ") + e.what());
        return 1;
    }
}

int ousd_stage_save(void* stage) {
    if (!stage) {
        SetLastError("Invalid stage handle");
        return 1;
    }
    
    try {
        OUSDStage* usdStage = static_cast<OUSDStage*>(stage);
        if (!usdStage->stage) {
            SetLastError("Invalid stage reference");
            return 1;
        }
        
        bool success = usdStage->stage->Save();
        if (!success) {
            SetLastError("Failed to save stage");
            return 1;
        }
        
        return 0;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception saving stage: ") + e.what());
        return 1;
    }
}

void ousd_stage_release(void* stage) {
    if (stage) {
        try {
            OUSDStage* usdStage = static_cast<OUSDStage*>(stage);
            delete usdStage;
        }
        catch (const std::exception& e) {
            SetLastError(std::string("Exception releasing stage: ") + e.what());
        }
    }
}

int ousd_define_prim(void* stage, const char* primPath, const char* typeName, void** outPrim) {
    if (!stage || !primPath || !typeName || !outPrim) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        OUSDStage* usdStage = static_cast<OUSDStage*>(stage);
        if (!usdStage->stage) {
            SetLastError("Invalid stage reference");
            return 1;
        }
        
        pxr::SdfPath path(primPath);
        pxr::TfToken type(typeName);
        
        pxr::UsdPrim prim = usdStage->stage->DefinePrim(path, type);
        if (!prim) {
            SetLastError("Failed to define prim");
            return 1;
        }
        
        *outPrim = new OUSDPrim(prim);
        return 0;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception defining prim: ") + e.what());
        return 1;
    }
}

int ousd_get_prim(void* stage, const char* primPath, void** outPrim) {
    if (!stage || !primPath || !outPrim) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        OUSDStage* usdStage = static_cast<OUSDStage*>(stage);
        if (!usdStage->stage) {
            SetLastError("Invalid stage reference");
            return 1;
        }
        
        pxr::SdfPath path(primPath);
        pxr::UsdPrim prim = usdStage->stage->GetPrimAtPath(path);
        if (!prim) {
            SetLastError("Prim not found at specified path");
            return 1;
        }
        
        *outPrim = new OUSDPrim(prim);
        return 0;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception getting prim: ") + e.what());
        return 1;
    }
}

int ousd_prim_is_valid(void* prim, int* outValid) {
    if (!prim || !outValid) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        OUSDPrim* usdPrim = static_cast<OUSDPrim*>(prim);
        *outValid = usdPrim->prim ? 1 : 0;
        return 0;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception checking prim validity: ") + e.what());
        return 1;
    }
}

int ousd_prim_get_name(void* prim, char* buffer, int bufferLen, int* outWritten) {
    if (!prim || !buffer || bufferLen <= 0 || !outWritten) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        OUSDPrim* usdPrim = static_cast<OUSDPrim*>(prim);
        if (!usdPrim->prim) {
            SetLastError("Invalid prim reference");
            return 1;
        }
        
        std::string name = usdPrim->prim.GetName().GetString();
        return CopyStringToBuffer(name, buffer, bufferLen, outWritten);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception getting prim name: ") + e.what());
        return 1;
    }
}

void ousd_prim_release(void* prim) {
    if (prim) {
        try {
            OUSDPrim* usdPrim = static_cast<OUSDPrim*>(prim);
            delete usdPrim;
        }
        catch (const std::exception& e) {
            SetLastError(std::string("Exception releasing prim: ") + e.what());
        }
    }
}

int ousd_prim_create_string_attribute(void* prim, const char* attrName) {
    if (!prim || !attrName) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        OUSDPrim* usdPrim = static_cast<OUSDPrim*>(prim);
        if (!usdPrim->prim) {
            SetLastError("Invalid prim reference");
            return 1;
        }
        
        pxr::TfToken token(attrName);
        pxr::UsdAttribute attr = usdPrim->prim.CreateAttribute(token, pxr::SdfValueTypeNames->String);
        if (!attr) {
            SetLastError("Failed to create attribute");
            return 1;
        }
        
        return 0;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception creating string attribute: ") + e.what());
        return 1;
    }
}

int ousd_prim_set_string_attribute(void* prim, const char* attrName, const char* value) {
    if (!prim || !attrName || !value) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        OUSDPrim* usdPrim = static_cast<OUSDPrim*>(prim);
        if (!usdPrim->prim) {
            SetLastError("Invalid prim reference");
            return 1;
        }
        
        pxr::TfToken token(attrName);
        pxr::UsdAttribute attr = usdPrim->prim.GetAttribute(token);
        if (!attr) {
            // Try to create it if it doesn't exist
            attr = usdPrim->prim.CreateAttribute(token, pxr::SdfValueTypeNames->String);
            if (!attr) {
                SetLastError("Failed to create or get attribute");
                return 1;
            }
        }
        
        bool success = attr.Set(pxr::VtValue(std::string(value)));
        if (!success) {
            SetLastError("Failed to set attribute value");
            return 1;
        }
        
        return 0;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception setting string attribute: ") + e.what());
        return 1;
    }
}

int ousd_prim_get_string_attribute(void* prim, const char* attrName, char* buffer, int bufferLen, int* outWritten, int* outFound) {
    if (!prim || !attrName || !buffer || bufferLen <= 0 || !outWritten || !outFound) {
        SetLastError("Invalid parameters");
        return 1;
    }
    
    try {
        OUSDPrim* usdPrim = static_cast<OUSDPrim*>(prim);
        if (!usdPrim->prim) {
            SetLastError("Invalid prim reference");
            return 1;
        }
        
        pxr::TfToken token(attrName);
        pxr::UsdAttribute attr = usdPrim->prim.GetAttribute(token);
        if (!attr) {
            *outFound = 0;
            buffer[0] = '\0';
            *outWritten = 1; // Just the null terminator
            return 0;
        }
        
        *outFound = 1;
        
        pxr::VtValue value;
        bool success = attr.Get(&value);
        if (!success) {
            SetLastError("Failed to get attribute value");
            return 1;
        }
        
        if (!value.IsHolding<std::string>()) {
            SetLastError("Attribute is not a string type");
            return 1;
        }
        
        std::string strValue = value.Get<std::string>();
        return CopyStringToBuffer(strValue, buffer, bufferLen, outWritten);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Exception getting string attribute: ") + e.what());
        return 1;
    }
}
