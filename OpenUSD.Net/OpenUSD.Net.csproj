<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <AssemblyName>OpenUSD.Net</AssemblyName>
    <RootNamespace>OpenUSD.Net</RootNamespace>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  
  <ItemGroup>
    <None Include="runtimes\**" Pack="true" PackagePath="runtimes" Condition="Exists('runtimes')" />
    <!-- Ensure native binaries are copied next to managed assembly for local runs -->
    <Content Include="runtimes\**" CopyToOutputDirectory="PreserveNewest" Condition="Exists('runtimes')" />
  </ItemGroup>
</Project>
