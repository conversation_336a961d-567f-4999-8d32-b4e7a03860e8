using System;
using System.Text;
using OpenUSD.Net.Interop;

namespace OpenUSD.Net
{
    /// <summary>
    /// Represents a USD prim, which is a scene graph object that can contain properties, attributes, and child prims.
    /// </summary>
    public class Prim : IDisposable
    {
        private PrimHandle _handle;
        private bool _disposed = false;
        private const int DefaultBufferSize = 1024;

        /// <summary>
        /// Gets the native handle to the prim.
        /// </summary>
        internal PrimHandle Handle => _handle;

        /// <summary>
        /// Initializes a new instance of the <see cref="Prim"/> class.
        /// </summary>
        /// <param name="handle">The native handle to the prim.</param>
        internal Prim(PrimHandle handle)
        {
            _handle = handle ?? throw new ArgumentNullException(nameof(handle));
        }

        /// <summary>
        /// Gets a value indicating whether this prim is valid.
        /// </summary>
        public bool IsValid
        {
            get
            {
                ThrowIfDisposed();

                int result = NativeMethods.ousd_prim_is_valid(_handle, out int valid);
                OpenUSDSafeHandle.ThrowIfError(result, "IsValid");

                return valid != 0;
            }
        }

        /// <summary>
        /// Gets the name of the prim.
        /// </summary>
        public string Name
        {
            get
            {
                ThrowIfDisposed();

                StringBuilder buffer = new StringBuilder(DefaultBufferSize);
                int result = NativeMethods.ousd_prim_get_name(_handle, buffer, buffer.Capacity, out int written);
                OpenUSDSafeHandle.ThrowIfError(result, "GetName");

                // If the buffer wasn't large enough, resize and try again
                if (written > buffer.Capacity)
                {
                    buffer.Capacity = written;
                    result = NativeMethods.ousd_prim_get_name(_handle, buffer, buffer.Capacity, out written);
                    OpenUSDSafeHandle.ThrowIfError(result, "GetName");
                }

                return buffer.ToString();
            }
        }

        /// <summary>
        /// Creates a string attribute on the prim.
        /// </summary>
        /// <param name="attributeName">The name of the attribute to create.</param>
        /// <exception cref="OpenUSDException">Thrown when the attribute creation fails.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the prim has been disposed.</exception>
        public void CreateStringAttribute(string attributeName)
        {
            ThrowIfDisposed();

            if (string.IsNullOrEmpty(attributeName))
                throw new ArgumentNullException(nameof(attributeName));

            int result = NativeMethods.ousd_prim_create_string_attribute(_handle, attributeName);
            OpenUSDSafeHandle.ThrowIfError(result, "CreateStringAttribute");
        }

        /// <summary>
        /// Sets a string attribute value on the prim.
        /// </summary>
        /// <param name="attributeName">The name of the attribute.</param>
        /// <param name="value">The value to set.</param>
        /// <exception cref="OpenUSDException">Thrown when the attribute setting fails.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the prim has been disposed.</exception>
        public void SetStringAttribute(string attributeName, string value)
        {
            ThrowIfDisposed();

            if (string.IsNullOrEmpty(attributeName))
                throw new ArgumentNullException(nameof(attributeName));

            if (value == null)
                value = string.Empty;

            int result = NativeMethods.ousd_prim_set_string_attribute(_handle, attributeName, value);
            OpenUSDSafeHandle.ThrowIfError(result, "SetStringAttribute");
        }

        /// <summary>
        /// Tries to get a string attribute value from the prim.
        /// </summary>
        /// <param name="attributeName">The name of the attribute.</param>
        /// <param name="value">When this method returns, contains the value of the attribute, if found; otherwise, the default value for the type.</param>
        /// <returns>True if the attribute was found; otherwise, false.</returns>
        /// <exception cref="OpenUSDException">Thrown when the attribute retrieval fails.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the prim has been disposed.</exception>
        public bool TryGetStringAttribute(string attributeName, out string value)
        {
            ThrowIfDisposed();

            if (string.IsNullOrEmpty(attributeName))
                throw new ArgumentNullException(nameof(attributeName));

            StringBuilder buffer = new StringBuilder(DefaultBufferSize);
            int result = NativeMethods.ousd_prim_get_string_attribute(_handle, attributeName, buffer, buffer.Capacity, out int written, out int found);
            OpenUSDSafeHandle.ThrowIfError(result, "GetStringAttribute");

            // If the buffer wasn't large enough, resize and try again
            if (written > buffer.Capacity)
            {
                buffer.Capacity = written;
                result = NativeMethods.ousd_prim_get_string_attribute(_handle, attributeName, buffer, buffer.Capacity, out written, out found);
                OpenUSDSafeHandle.ThrowIfError(result, "GetStringAttribute");
            }

            value = found != 0 ? buffer.ToString() : null;
            return found != 0;
        }

        /// <summary>
        /// Gets a string attribute value from the prim.
        /// </summary>
        /// <param name="attributeName">The name of the attribute.</param>
        /// <returns>The attribute value.</returns>
        /// <exception cref="OpenUSDException">Thrown when the attribute retrieval fails or the attribute is not found.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the prim has been disposed.</exception>
        public string GetStringAttribute(string attributeName)
        {
            if (TryGetStringAttribute(attributeName, out string value))
            {
                return value;
            }
            
            throw new OpenUSDException($"Attribute '{attributeName}' not found");
        }

        /// <summary>
        /// Throws an <see cref="ObjectDisposedException"/> if the prim has been disposed.
        /// </summary>
        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(Prim));
        }

        #region IDisposable Implementation

        /// <summary>
        /// Finalizer.
        /// </summary>
        ~Prim()
        {
            Dispose(false);
        }

        /// <summary>
        /// Disposes the prim and releases all resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the prim and releases all resources.
        /// </summary>
        /// <param name="disposing">True if called from Dispose(); false if called from the finalizer.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                }

                // Dispose unmanaged resources
                _handle?.Dispose();
                _handle = null;
                _disposed = true;
            }
        }

        #endregion
    }
}
