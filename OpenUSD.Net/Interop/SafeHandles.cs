using System;
using System.Runtime.InteropServices;

namespace OpenUSD.Net.Interop
{
    /// <summary>
    /// Exception thrown when an OpenUSD operation fails.
    /// </summary>
    public class OpenUSDException : Exception
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="OpenUSDException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        public OpenUSDException(string message) : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OpenUSDException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="innerException">The inner exception.</param>
        public OpenUSDException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    /// <summary>
    /// Base class for OpenUSD safe handles.
    /// </summary>
    public abstract class OpenUSDSafeHandle : SafeHandle
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="OpenUSDSafeHandle"/> class.
        /// </summary>
        protected OpenUSDSafeHandle() : base(IntPtr.Zero, true)
        {
        }

        /// <summary>
        /// Gets a value indicating whether the handle is invalid.
        /// </summary>
        public override bool IsInvalid => handle == IntPtr.Zero;

        /// <summary>
        /// Throws an exception if the native operation failed.
        /// </summary>
        /// <param name="errorCode">The error code returned by the native method.</param>
        /// <param name="operationName">The name of the operation that failed.</param>
        public static void ThrowIfError(int errorCode, string operationName)
        {
            if (errorCode != 0)
            {
                string errorMessage = NativeMethods.GetLastError();
                throw new OpenUSDException($"{operationName} failed: {errorMessage}");
            }
        }
    }

    /// <summary>
    /// Safe handle for OpenUSD stage objects.
    /// </summary>
    public class StageHandle : OpenUSDSafeHandle
    {
        /// <summary>
        /// Releases the stage handle.
        /// </summary>
        /// <returns>True if the handle was released successfully; otherwise, false.</returns>
        protected override bool ReleaseHandle()
        {
            if (!IsInvalid)
            {
                NativeMethods.ousd_stage_release(handle);
                return true;
            }
            return false;
        }

        /// <summary>
        /// Implicit conversion to IntPtr.
        /// </summary>
        /// <param name="handle">The handle to convert.</param>
        public static implicit operator IntPtr(StageHandle handle)
        {
            return handle?.handle ?? IntPtr.Zero;
        }
    }

    /// <summary>
    /// Safe handle for OpenUSD prim objects.
    /// </summary>
    public class PrimHandle : OpenUSDSafeHandle
    {
        /// <summary>
        /// Releases the prim handle.
        /// </summary>
        /// <returns>True if the handle was released successfully; otherwise, false.</returns>
        protected override bool ReleaseHandle()
        {
            if (!IsInvalid)
            {
                NativeMethods.ousd_prim_release(handle);
                return true;
            }
            return false;
        }

        /// <summary>
        /// Implicit conversion to IntPtr.
        /// </summary>
        /// <param name="handle">The handle to convert.</param>
        public static implicit operator IntPtr(PrimHandle handle)
        {
            return handle?.handle ?? IntPtr.Zero;
        }
    }
}
