using System;
using System.Runtime.InteropServices;
using System.Text;

namespace OpenUSD.Net.Interop
{
    /// <summary>
    /// Provides P/Invoke declarations for the native OpenUSD C API.
    /// </summary>
    internal static class NativeMethods
    {
        /// <summary>
        /// The name of the native library.
        /// </summary>
        private const string LibraryName = "ousd_native";

        #region Stage Functions

        /// <summary>
        /// Creates a new USD stage at the specified path.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        internal static extern int ousd_stage_create_new(
            [MarshalAs(UnmanagedType.LPUTF8Str)] string path,
            out StageHandle stage);

        /// <summary>
        /// Opens an existing USD stage from the specified path.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        internal static extern int ousd_stage_open(
            [MarshalAs(UnmanagedType.LPUTF8Str)] string path,
            out StageHandle stage);

        /// <summary>
        /// Saves the stage to disk.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl)]
        internal static extern int ousd_stage_save(StageHandle stage);

        /// <summary>
        /// Releases the stage and frees associated resources.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl)]
        internal static extern void ousd_stage_release(IntPtr stage);

        #endregion

        #region Prim Functions

        /// <summary>
        /// Defines a new prim on the stage.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        internal static extern int ousd_define_prim(
            StageHandle stage,
            [MarshalAs(UnmanagedType.LPUTF8Str)] string primPath,
            [MarshalAs(UnmanagedType.LPUTF8Str)] string typeName,
            out PrimHandle prim);

        /// <summary>
        /// Gets an existing prim from the stage.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        internal static extern int ousd_get_prim(
            StageHandle stage,
            [MarshalAs(UnmanagedType.LPUTF8Str)] string primPath,
            out PrimHandle prim);

        /// <summary>
        /// Checks if a prim handle is valid.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl)]
        internal static extern int ousd_prim_is_valid(
            PrimHandle prim,
            out int valid);

        /// <summary>
        /// Gets the name of a prim.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl)]
        internal static extern int ousd_prim_get_name(
            PrimHandle prim,
            StringBuilder buffer,
            int bufferLen,
            out int written);

        /// <summary>
        /// Releases the prim and frees associated resources.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl)]
        internal static extern void ousd_prim_release(IntPtr prim);

        #endregion

        #region Attribute Functions

        /// <summary>
        /// Creates a string attribute on a prim.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        internal static extern int ousd_prim_create_string_attribute(
            PrimHandle prim,
            [MarshalAs(UnmanagedType.LPUTF8Str)] string attrName);

        /// <summary>
        /// Sets a string attribute value on a prim.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        internal static extern int ousd_prim_set_string_attribute(
            PrimHandle prim,
            [MarshalAs(UnmanagedType.LPUTF8Str)] string attrName,
            [MarshalAs(UnmanagedType.LPUTF8Str)] string value);

        /// <summary>
        /// Gets a string attribute value from a prim.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        internal static extern int ousd_prim_get_string_attribute(
            PrimHandle prim,
            [MarshalAs(UnmanagedType.LPUTF8Str)] string attrName,
            StringBuilder buffer,
            int bufferLen,
            out int written,
            out int found);

        #endregion

        #region Error Handling

        /// <summary>
        /// Gets the last error message.
        /// </summary>
        [DllImport(LibraryName, CallingConvention = CallingConvention.Cdecl)]
        internal static extern IntPtr ousd_last_error_message();

        /// <summary>
        /// Gets the last error message as a managed string.
        /// </summary>
        /// <returns>The last error message.</returns>
        internal static string GetLastError()
        {
            IntPtr ptr = ousd_last_error_message();
            return Marshal.PtrToStringUTF8(ptr) ?? string.Empty;
        }

        #endregion
    }
}
