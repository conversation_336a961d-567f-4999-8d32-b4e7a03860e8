using System;
using System.Text;
using OpenUSD.Net.Interop;

namespace OpenUSD.Net
{
    /// <summary>
    /// Represents a USD stage, which is the primary container for scene description.
    /// </summary>
    public class Stage : IDisposable
    {
        private StageHandle _handle;
        private bool _disposed = false;

        /// <summary>
        /// Gets the native handle to the stage.
        /// </summary>
        internal StageHandle Handle => _handle;

        /// <summary>
        /// Initializes a new instance of the <see cref="Stage"/> class.
        /// </summary>
        /// <param name="handle">The native handle to the stage.</param>
        private Stage(StageHandle handle)
        {
            _handle = handle ?? throw new ArgumentNullException(nameof(handle));
        }

        /// <summary>
        /// Creates a new USD stage at the specified path.
        /// </summary>
        /// <param name="path">The file path where the stage will be saved.</param>
        /// <returns>A new <see cref="Stage"/> instance.</returns>
        /// <exception cref="OpenUSDException">Thrown when the stage creation fails.</exception>
        public static Stage CreateNew(string path)
        {
            if (string.IsNullOrEmpty(path))
                throw new ArgumentNullException(nameof(path));

            int result = NativeMethods.ousd_stage_create_new(path, out StageHandle handle);
            OpenUSDSafeHandle.ThrowIfError(result, "CreateNew");

            return new Stage(handle);
        }

        /// <summary>
        /// Opens an existing USD stage from the specified path.
        /// </summary>
        /// <param name="path">The file path to the USD file.</param>
        /// <returns>A new <see cref="Stage"/> instance.</returns>
        /// <exception cref="OpenUSDException">Thrown when the stage opening fails.</exception>
        public static Stage Open(string path)
        {
            if (string.IsNullOrEmpty(path))
                throw new ArgumentNullException(nameof(path));

            int result = NativeMethods.ousd_stage_open(path, out StageHandle handle);
            OpenUSDSafeHandle.ThrowIfError(result, "Open");

            return new Stage(handle);
        }

        /// <summary>
        /// Saves the stage to disk.
        /// </summary>
        /// <exception cref="OpenUSDException">Thrown when the save operation fails.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the stage has been disposed.</exception>
        public void Save()
        {
            ThrowIfDisposed();

            int result = NativeMethods.ousd_stage_save(_handle);
            OpenUSDSafeHandle.ThrowIfError(result, "Save");
        }

        /// <summary>
        /// Defines a new prim on the stage.
        /// </summary>
        /// <param name="primPath">The path for the prim (e.g., "/Root/MyPrim").</param>
        /// <param name="typeName">The type name (e.g., "Xform", "Mesh", etc.).</param>
        /// <returns>A new <see cref="Prim"/> instance.</returns>
        /// <exception cref="OpenUSDException">Thrown when the prim definition fails.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the stage has been disposed.</exception>
        public Prim DefinePrim(string primPath, string typeName)
        {
            ThrowIfDisposed();

            if (string.IsNullOrEmpty(primPath))
                throw new ArgumentNullException(nameof(primPath));
            
            if (string.IsNullOrEmpty(typeName))
                throw new ArgumentNullException(nameof(typeName));

            int result = NativeMethods.ousd_define_prim(_handle, primPath, typeName, out PrimHandle primHandle);
            OpenUSDSafeHandle.ThrowIfError(result, "DefinePrim");

            return new Prim(primHandle);
        }

        /// <summary>
        /// Gets an existing prim from the stage.
        /// </summary>
        /// <param name="primPath">The path for the prim.</param>
        /// <returns>A new <see cref="Prim"/> instance, or null if the prim does not exist.</returns>
        /// <exception cref="OpenUSDException">Thrown when the operation fails.</exception>
        /// <exception cref="ObjectDisposedException">Thrown when the stage has been disposed.</exception>
        public Prim GetPrim(string primPath)
        {
            ThrowIfDisposed();

            if (string.IsNullOrEmpty(primPath))
                throw new ArgumentNullException(nameof(primPath));

            int result = NativeMethods.ousd_get_prim(_handle, primPath, out PrimHandle primHandle);
            
            // If the prim doesn't exist, return null instead of throwing
            if (result != 0)
            {
                string error = NativeMethods.GetLastError();
                if (error.Contains("not found"))
                {
                    return null;
                }
                
                OpenUSDSafeHandle.ThrowIfError(result, "GetPrim");
            }

            return new Prim(primHandle);
        }

        /// <summary>
        /// Throws an <see cref="ObjectDisposedException"/> if the stage has been disposed.
        /// </summary>
        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(Stage));
        }

        #region IDisposable Implementation

        /// <summary>
        /// Finalizer.
        /// </summary>
        ~Stage()
        {
            Dispose(false);
        }

        /// <summary>
        /// Disposes the stage and releases all resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the stage and releases all resources.
        /// </summary>
        /// <param name="disposing">True if called from Dispose(); false if called from the finalizer.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                }

                // Dispose unmanaged resources
                _handle?.Dispose();
                _handle = null;
                _disposed = true;
            }
        }

        #endregion
    }
}
